<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import {
  <PERSON>Header,
  Button,
  Card,
  Descriptions,
  DescriptionsItem,
  Table,
} from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { getOrderInfo } from '#/api/manageModule';
import { useRoute } from 'vue-router';
import { ref, toRefs, onMounted } from 'vue';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());
import { useTabs } from '@vben/hooks';
const { setTabTitle } = useTabs();
import OrderLog from './modules/orderLog.vue';
import VerifyLog from './modules/verifyLog.vue';
import Refund from './modules/refund.vue';
import Coupons from './modules/coupons.vue';

const route = useRoute();
const orderId = ref<any>(route.query.id);
const orderInfo = ref<any>({});
const getOrderInfoData = async () => {
  const res = await getOrderInfo(orderId.value);
  orderInfo.value = res;
};

onMounted(() => {
  // 设置页面标签标题
  setTabTitle('门票订单详情-' + orderId.value);
  getOrderInfoData();
});

const orderLog = ref<any>(null);
const resetData = () => {
  getOrderInfoData();
  orderLog.value.getOrderLogList();
};

// 核销记录弹窗
const [VerifyLogModal, verifyLogModalApi] = useVbenModal({
  connectedComponent: VerifyLog,
  destroyOnClose: true,
});

const showVerifyLog = (orderDetailId: any, orderId: any, orderItemId: any) => {
  verifyLogModalApi
    .setData({
      orderDetailId,
      orderId,
      orderItemId,
    })
    .open();
};

// 退款弹窗
const [RefundModal, refundModalApi] = useVbenModal({
  connectedComponent: Refund,
  destroyOnClose: true,
});

const orderRefund = (type: string, data: any) => {
  refundModalApi.setData({ type, data }).open();
};

// 优惠券弹窗
const [CouponModal, couponModalApi] = useVbenModal({
  connectedComponent: Coupons,
  destroyOnClose: true,
});
// 查看优惠券详情
const showCouponInfo = () => {
  couponModalApi.setData({ orderId: orderId.value }).open();
};

const filterText = (arr: any[], val: any) => {
  return arr.find((item: any) => item.value === val)?.label;
};

const filterValidText = (val: any, data: any) => {
  let text = filterText(accessAllEnums.value.ticketValidType.list, val);
  if (val == 1) {
    return text;
  } else if (val == 5) {
    return data.validBeginDate + '~' + data.validEndDate;
  } else {
    return text.replace('X', data.validDayNum);
  }
};
// 门票
const columns1: TableColumnType[] = [
  {
    title: '核销码',
    dataIndex: 'verificationCode',
    width: 180,
  },
  {
    title: '出行人',
    dataIndex: 'touristName',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristName || '--';
    },
  },
  {
    title: '身份证',
    dataIndex: 'touristIdcard',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristIdcard || '--';
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.ticketOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderRefundStatus.list.find(
        (item: any) => item.value === record.refundStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '核销总数',
    dataIndex: 'verificationTotal',
    width: 180,
    align: 'center',
  },
  {
    title: '剩余可核销数',
    dataIndex: 'canVerificationNum',
    width: 180,
    align: 'center',
  },
  {
    title: '最后核销时间',
    dataIndex: 'lastVerificationTime',
    width: 180,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    align: 'center',
  },
];
// 套票
const columns2: TableColumnType[] = [
  {
    title: '核销码',
    dataIndex: 'verificationCode',
    width: 260,
  },
  {
    title: '门票名称',
    dataIndex: 'ticketName',
    width: 180,
    customRender: ({ record }: any) => {
      if (record.hasChild == 1) {
        return '';
      }
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.ticketOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderRefundStatus.list.find(
        (item: any) => item.value === record.refundStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '核销总数',
    dataIndex: 'verificationTotal',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      if (record.hasChild == 1) {
        return '';
      }
    },
  },
  {
    title: '剩余可核销数',
    dataIndex: 'canVerificationNum',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      if (record.hasChild == 1) {
        return '';
      }
    },
  },
  {
    title: '出行人',
    dataIndex: 'touristName',
    width: 180,
    align: 'center',
  },
  {
    title: '最后核销时间',
    dataIndex: 'lastVerificationTime',
    width: 180,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    align: 'center',
  },
];
const columns3: TableColumnType[] = [
  {
    title: '卡号',
    dataIndex: 'cardNo',
    width: 180,
  },
  {
    title: '核销码',
    dataIndex: 'verificationCode',
    width: 180,
  },
  {
    title: '出行人',
    dataIndex: 'touristName',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristName || '--';
    },
  },
  {
    title: '身份证',
    dataIndex: 'touristIdcard',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristIdcard || '--';
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.ticketOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderRefundStatus.list.find(
        (item: any) => item.value === record.refundStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    align: 'center',
  },
];

// 卡
const cardColumns: TableColumnType[] = [
  {
    title: '门票名称',
    dataIndex: 'ticketName',
    align: 'center',
    width: '50%',
  },
  {
    title: '使用次数',
    dataIndex: 'limit',
    align: 'center',
    width: '50%',
  },
];

// 订单状态不可退集合
const noOrderStatuses = ref([1, 6, 7, 10, 13]);
// 退款状态下可退集合
const refundStatuses = ref([0, 2, 5]);

const isShow = (refundStatus: any) => {
  if (!noOrderStatuses.value.includes(orderInfo.value.orderStatus)) {
    if (refundStatus) {
      if (refundStatuses.value.includes(refundStatus)) {
        return true;
      } else {
        return false;
      }
    }
    return true;
  } else {
    return false;
  }
};

const isShowBtn = (arr: any[]) => {
  if (!noOrderStatuses.value.includes(orderInfo.value.orderStatus)) {
    // 循环数组，只要一个满足条件，就返回true
    return arr.some((item: any) => {
      if (item.refundStatus) {
        if (refundStatuses.value.includes(item.refundStatus)) {
          return true;
        } else {
          return false;
        }
      }
      return true;
    });
  } else {
    return false;
  }
};
</script>
<template>
  <Page auto-content-height>
    <div class="bg-card">
      <PageHeader
        title="订单详情"
        class="p-3"
        @back="() => $router.back()"
      ></PageHeader>
      <div class="px-3 pb-3">
        <Card title="订单信息" class="mb-4">
          <template #extra>
            <Button v-if="isShow(null)" @click="orderRefund('all', orderInfo)"
              >全部退款</Button
            >
          </template>
          <Descriptions>
            <DescriptionsItem label="订单号">{{
              orderInfo.orderNo
            }}</DescriptionsItem>
            <DescriptionsItem label="下单时间">{{
              orderInfo.orderTime
            }}</DescriptionsItem>
            <DescriptionsItem label="订单状态">{{
              filterText(
                accessAllEnums?.ticketOrderStatus.list,
                orderInfo.orderStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付状态">{{
              filterText(
                accessAllEnums?.orderPayStatus.list,
                orderInfo.payStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付方式">{{
              filterText(
                accessAllEnums?.orderPayMethod.list,
                orderInfo.payMethod,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="订单来源">{{
              filterText(
                accessAllEnums?.ticketOrderSource.list,
                orderInfo.orderSource,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="联系人">
              {{ orderInfo.userInfo?.name }}
              <span class="ml-2" v-if="orderInfo.userInfo?.phone">{{
                orderInfo.userInfo?.phone
              }}</span>
            </DescriptionsItem>
            <DescriptionsItem label="订单金额">
              {{ orderInfo.orderPrice }}
            </DescriptionsItem>
            <DescriptionsItem label="优惠金额">
              {{ orderInfo.discountPrice }}
              <Button
                type="link"
                size="small"
                v-if="orderInfo.userCouponId > 0"
                @click="showCouponInfo()"
                >查看</Button
              >
            </DescriptionsItem>
            <DescriptionsItem label="实付金额">
              {{ orderInfo.actualPrice }}
            </DescriptionsItem>
          </Descriptions>
        </Card>
        <template
          v-for="(item, index) in orderInfo.orderItemList"
          :key="item.id"
        >
          <Card class="mb-5">
            <div class="mb-2 flex justify-between">
              <h3 class="text-[16px] font-[600]">门票信息</h3>
              <Button
                v-if="isShowBtn(item.ticketOrderDetailList)"
                @click="orderRefund('initiative', item)"
                >主动退款</Button
              >
            </div>
            <Descriptions>
              <DescriptionsItem label="门票名称">{{
                item.ticketName +
                '（' +
                filterText(accessAllEnums.ticketModel.list, item.model) +
                '）'
              }}</DescriptionsItem>
              <DescriptionsItem label="单价">{{
                item.unitPrice
              }}</DescriptionsItem>
              <DescriptionsItem label="数量">{{
                item.ticketNum
              }}</DescriptionsItem>
              <DescriptionsItem label="实名制">{{
                filterText(
                  accessAllEnums.tickeAuthenticationType.list,
                  item.authenticationType,
                )
              }}</DescriptionsItem>
              <DescriptionsItem label="退票规则">{{
                filterText(
                  accessAllEnums.ticketRefundType.list,
                  item.refundType,
                )
              }}</DescriptionsItem>
              <DescriptionsItem label="有效期">
                <!-- {{ filterValidText(item.validType, item) }} -->
                {{
                  item.validType == 1
                    ? item.validBeginDate + '当天有效'
                    : item.validBeginDate
                      ? item.validBeginDate + '~' + item.validEndDate
                      : ''
                }}
              </DescriptionsItem>
              <DescriptionsItem label="场次" v-if="item.periodId > 0">{{
                item.periodBeginTime + '~' + item.periodEndTime
              }}</DescriptionsItem>
              <DescriptionsItem label="二维码规则">
                {{
                  filterText(
                    accessAllEnums.tickeQrcodeRule.list,
                    item.qrcodeRule,
                  )
                }}
              </DescriptionsItem>
            </Descriptions>
            <Table
              :columns="cardColumns"
              :pagination="false"
              :data-source="item.childList"
              v-if="[3, 4, 5].includes(item.model)"
              class="mb-5"
              bordered
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex == 'limit'">
                  <p v-if="record.isLimit == 0">不限次数</p>
                  <p v-else>
                    共{{ record.totalLimit }}次，每月最多{{
                      record.monthLimit
                    }}次，每日最多{{ record.dayLimit }}次
                  </p>
                </template>
              </template>
            </Table>
            <div class="mb-2 text-[16px] font-[600]">订单明细</div>
            <!-- 门票订单明细表格 -->
            <Table
              :columns="columns1"
              :data-source="item.ticketOrderDetailList"
              :pagination="false"
              v-if="item.model == 1"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex == 'lastVerificationTime'">
                  <p v-if="record.lastVerificationTime">
                    {{ record.lastVerificationTime }} <br />
                    <Button
                      type="link"
                      size="small"
                      @click="
                        showVerifyLog(
                          record.id,
                          record.orderId,
                          record.orderItemId,
                        )
                      "
                      >查看记录</Button
                    >
                  </p>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <Button
                    type="link"
                    size="small"
                    v-if="isShow(record.refundStatus)"
                    @click="orderRefund('initiative', record)"
                    >主动退款</Button
                  >
                  <Button
                    type="link"
                    size="small"
                    v-if="isShow(record.refundStatus)"
                    @click="orderRefund('part', record)"
                    >部分退款</Button
                  >
                </template>
              </template>
            </Table>
            <!-- 套票订单明细表格 -->
            <Table
              :columns="columns2"
              :data-source="item.ticketOrderDetailList"
              :pagination="false"
              childrenColumnName="orderDetailChildList"
              :indentSize="10"
              defaultExpandAllRows
              v-if="item.model == 2"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'touristName'">
                  <div
                    v-if="record.hasChild == 0 && !record.orderDetailChildList"
                  >
                    <p>{{ record.touristName }}</p>
                    <p>{{ record.touristIdcard }}</p>
                  </div>
                  <div v-else>
                    <p>{{ record.hasChild == 1 ? '' : record.touristName }}</p>
                    <p>
                      {{ record.hasChild == 1 ? '' : record.touristIdcard }}
                    </p>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <div v-if="record.hasChild == 1">
                    <Button
                      type="link"
                      size="small"
                      v-if="isShow(record.refundStatus)"
                      @click="orderRefund('initiative', record)"
                      >主动退款</Button
                    >
                    <Button
                      type="link"
                      size="small"
                      v-if="isShow(record.refundStatus)"
                      @click="orderRefund('part', record)"
                      >部分退款</Button
                    >
                  </div>
                  <Button
                    type="link"
                    size="small"
                    v-if="record.orderStatus == 6 && record.hasChild == 0"
                    >查看记录</Button
                  >
                </template>
              </template>
            </Table>
            <!-- 卡类订单明细表格 -->
            <Table
              :columns="columns3"
              :data-source="item.ticketOrderDetailList"
              :pagination="false"
              v-if="[3, 4, 5].includes(item.model)"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'action'">
                  <Button
                    type="link"
                    size="small"
                    v-if="isShow(record.refundStatus)"
                    @click="orderRefund('initiative', record)"
                    >主动退款</Button
                  >
                  <Button
                    type="link"
                    size="small"
                    v-if="isShow(record.refundStatus)"
                    @click="orderRefund('part', record)"
                    >部分退款</Button
                  >
                  <Button
                    type="link"
                    size="small"
                    @click="
                      showVerifyLog(
                        record.id,
                        record.orderId,
                        record.orderItemId,
                      )
                    "
                    >查看记录</Button
                  >
                </template>
              </template>
            </Table>
          </Card>
        </template>
        <OrderLog ref="orderLog" :orderId="orderId"></OrderLog>
      </div>
    </div>
    <VerifyLogModal></VerifyLogModal>
    <CouponModal></CouponModal>
    <RefundModal @success="resetData"></RefundModal>
  </Page>
</template>
