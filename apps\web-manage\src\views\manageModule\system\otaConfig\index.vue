<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { Page } from '@vben/common-ui';
import { InputSearch, Table } from 'ant-design-vue';
import { getAllScenicList } from '#/api/manageModule';

const searchValue = ref(null);
const scenicList = ref([]);
const columns=[
  {
    title: '景区名称',
    dataIndex: 'scenicName',
  },
  {
    title: 'OTA名称',
    dataIndex: 'otaName',
  },
  {
    title: 'OTA账号',
    dataIndex: 'otaAccount',
  },
  {
    title: 'OTA密码',
    dataIndex: 'otaPassword',
  },
  {
    title: 'OTA状态',
    dataIndex: 'otaStatus',
  },
  {

const onSearch = (val: string) => {
  console.log(val);
};

const getScenicList = async () => {
  const res = await getAllScenicList({ name: searchValue.value });
  console.log(res);
  scenicList.value = res;
};
onMounted(() => {
  getScenicList();
});
</script>
<template>
  <Page auto-content-height>
    <div class="h-full flex gap-2 bg-card p-2">
      <div>
        <InputSearch
          v-model:value="searchValue"
          placeholder="请输入景区名称"
          class="w-[300px]"
          @search="onSearch"
        />
        <Table class="mt-2" :columns="columns" :data-source="data" :pagination="false"></Table>
      </div>
      <div>右侧</div>
    </div>
  </Page>
</template>
