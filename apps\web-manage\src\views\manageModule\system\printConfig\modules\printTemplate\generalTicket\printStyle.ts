export function printStyle() {
    return `
    <style>
    .flex{
      display: flex;
    }
    .flex-1{
      flex: 1;
    }
    .flex-row{
      flex-direction: row;
    }
    .flex-col{
      flex-direction: column;
    }
    .flex-shrink-0{
      flex-shrink: 0;
    }
    .justify-between{
      justify-content: space-between;
    }
    .justify-center{
      justify-content: center;
    }
    .items-center{
      align-items: center;
    }
    .bg-white{
      background-color: white;
    }
    .shadow-lg{
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .mt-2{
      margin-top: 0.5rem;
    }
    .mb-2{
      margin-bottom: 0.5rem;
    }
    .mb-1{
      margin-bottom: 0.25rem;
    }
    .ml-2{
      margin-left: 0.5rem;
    }
    .mr-2{
      margin-right: 0.5rem;
    }
    .p-0{
      padding: 0;
    }
    .break-all{
      word-break: break-all;
    }
    .block{
      display: block;
    }
    .w-full{
      width: 100%;
    }
    .w-60{
      width: 55px;
      display:flex;
    }
    .lastJustify span{
      flex: 1;
    }
    </style>
`
}

// .w-60::after{
//       content: '';
//       width: 100%;
//       display: inline-block;
//     }
//     .lastJustify{
//       text-align: justify;
//       width:60px;
//     }